// Script untuk extract customer data dari QuinosCloud
// Menggunakan Playwright MCP dari Augment

async function waitForLogin() {
    console.log('🔐 Menunggu login...');

    // Check if already logged in by looking for admin elements
    let loginComplete = false;
    let attempts = 0;
    const maxAttempts = 60; // Wait up to 60 seconds

    while (!loginComplete && attempts < maxAttempts) {
        const currentUrl = await page.url();

        // Check if URL changed from login page
        if (!currentUrl.includes('/login')) {
            loginComplete = true;
            console.log('✅ Login berhasil terdeteksi!');
            break;
        }

        // Check for admin elements
        const adminElements = await page.$$('[href*="admin"], .admin, #admin');
        if (adminElements.length > 0) {
            loginComplete = true;
            console.log('✅ Halaman admin terdeteksi!');
            break;
        }

        await page.waitForTimeout(1000);
        attempts++;

        if (attempts % 10 === 0) {
            console.log(`⏳ Masih menunggu login... (${attempts}s)`);
        }
    }

    if (!loginComplete) {
        throw new Error('❌ Timeout menunggu login. Silakan login manual dan jalankan ulang script.');
    }

    return loginComplete;
}

async function extractCustomerDataAfterLogin() {
    console.log('=== QuinosCloud Customer Data Extractor ===');
    console.log('🌐 Browser sudah terbuka di halaman login');
    console.log('📝 Silakan login manual, script akan otomatis detect setelah login berhasil');

    // Wait for login to complete
    await waitForLogin();

    console.log('🚀 Mulai extract data customer...');

    // Check current page
    const currentUrl = await page.url();
    console.log('Current URL:', currentUrl);

    // Navigate to export customer page if not already there
    if (!currentUrl.includes('exportCustomer')) {
        console.log('Navigating to export customer page...');
        await page.goto('https://quinoscloud.com/cloud/admin#/exportCustomer');
        await page.waitForTimeout(3000);
    }

    // Take screenshot to see current state
    await page.screenshot({ path: 'current-page.png', fullPage: true });
    console.log('Screenshot saved as current-page.png');

    // Look for customer table or data
    const tables = await page.$$('table');
    console.log(`Found ${tables.length} tables on the page`);

    if (tables.length > 0) {
        // Extract data from the first table (usually the main data table)
        const tableData = await page.evaluate(() => {
            const table = document.querySelector('table');
            if (!table) return [];

            const rows = table.querySelectorAll('tr');
            const data = [];

            rows.forEach((row, index) => {
                const cells = row.querySelectorAll('td, th');
                if (cells.length > 0) {
                    const rowData = Array.from(cells).map(cell =>
                        cell.textContent.trim().replace(/,/g, ';')
                    );
                    data.push(rowData);
                }
            });

            return data;
        });

        if (tableData.length > 0) {
            // Save as CSV
            const csvContent = tableData.map(row => row.join(',')).join('\n');
            const fs = require('fs');
            const filename = `customer-data-${new Date().toISOString().split('T')[0]}.csv`;

            fs.writeFileSync(filename, csvContent, 'utf8');
            console.log(`✅ Data berhasil disimpan ke ${filename}`);
            console.log(`📊 Total baris: ${tableData.length}`);

            // Show preview
            console.log('\n📋 Preview data (5 baris pertama):');
            tableData.slice(0, 5).forEach((row, index) => {
                console.log(`${index + 1}: ${row.join(' | ')}`);
            });
        } else {
            console.log('❌ Tidak ada data ditemukan di tabel');
        }
    } else {
        console.log('❌ Tidak ada tabel ditemukan di halaman');

        // Try to find export/download buttons
        const exportButtons = await page.$$('button:has-text("Export"), button:has-text("Download"), button:has-text("CSV")');

        if (exportButtons.length > 0) {
            console.log(`🔍 Ditemukan ${exportButtons.length} tombol export/download`);
            console.log('Mencoba klik tombol export...');

            await exportButtons[0].click();
            await page.waitForTimeout(3000);

            console.log('Tombol export diklik, cek download folder untuk file CSV');
        } else {
            console.log('❌ Tidak ada tombol export ditemukan');
        }
    }

    // Get page content for analysis
    const pageContent = await page.content();
    console.log('\n📄 Analisis halaman:');
    console.log(`- Ukuran konten: ${pageContent.length} karakter`);
    console.log(`- Mengandung kata "customer": ${pageContent.toLowerCase().includes('customer')}`);
    console.log(`- Mengandung kata "export": ${pageContent.toLowerCase().includes('export')}`);
    console.log(`- Mengandung kata "download": ${pageContent.toLowerCase().includes('download')}`);
}

// Export function untuk digunakan dengan Playwright MCP
module.exports = { extractCustomerDataAfterLogin };
