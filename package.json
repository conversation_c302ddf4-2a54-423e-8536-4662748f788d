{"name": "datacust-extractor", "version": "1.0.0", "description": "Customer data extractor for QuinosCloud using Playwright", "main": "extract-customer-data.js", "scripts": {"start": "node extract-customer-data.js", "install-playwright": "npx playwright install", "extract": "node extract-customer-data.js"}, "dependencies": {"playwright": "^1.40.0"}, "devDependencies": {}, "keywords": ["playwright", "scraping", "csv", "customer-data"], "author": "", "license": "MIT"}