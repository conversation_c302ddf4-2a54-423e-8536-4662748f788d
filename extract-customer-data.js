const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

async function extractCustomerData() {
    const browser = await chromium.launch({ 
        headless: false, // Set to true for production
        slowMo: 1000 // Slow down for debugging
    });
    
    const context = await browser.newContext({
        // Add user agent to avoid detection
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    });
    
    const page = await context.newPage();
    
    try {
        console.log('Navigating to QuinosCloud admin page...');
        await page.goto('https://quinoscloud.com/cloud/admin#/exportCustomer', {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        // Wait for page to load completely
        await page.waitForTimeout(3000);
        
        // Check if login is required
        const loginForm = await page.$('input[type="password"], input[name="password"], .login-form');
        if (loginForm) {
            console.log('Login required. Please login manually and press Enter to continue...');
            await new Promise(resolve => {
                process.stdin.once('data', () => resolve());
            });
        }
        
        // Wait for customer data to load
        console.log('Waiting for customer data to load...');
        await page.waitForTimeout(5000);
        
        // Look for export button or CSV download functionality
        const exportButton = await page.$('button:has-text("Export"), button:has-text("Download"), button:has-text("CSV"), .export-btn, .download-btn');
        
        if (exportButton) {
            console.log('Found export button, clicking...');
            await exportButton.click();
            await page.waitForTimeout(2000);
        }
        
        // Try to find customer table or data
        const customerTable = await page.$('table, .customer-table, .data-table, .grid');
        
        if (customerTable) {
            console.log('Found customer table, extracting data...');
            
            // Extract table data
            const tableData = await page.evaluate(() => {
                const tables = document.querySelectorAll('table, .customer-table, .data-table, .grid');
                let data = [];
                
                for (let table of tables) {
                    const rows = table.querySelectorAll('tr, .row');
                    
                    for (let row of rows) {
                        const cells = row.querySelectorAll('td, th, .cell');
                        if (cells.length > 0) {
                            const rowData = Array.from(cells).map(cell => 
                                cell.textContent.trim().replace(/,/g, ';') // Replace commas to avoid CSV issues
                            );
                            data.push(rowData);
                        }
                    }
                }
                
                return data;
            });
            
            if (tableData.length > 0) {
                // Convert to CSV format
                const csvContent = tableData.map(row => row.join(',')).join('\n');
                
                // Save to file
                const filename = `customer-data-${new Date().toISOString().split('T')[0]}.csv`;
                fs.writeFileSync(filename, csvContent, 'utf8');
                console.log(`Customer data saved to ${filename}`);
                console.log(`Total rows extracted: ${tableData.length}`);
                
                // Show preview of first few rows
                console.log('\nPreview of extracted data:');
                tableData.slice(0, 5).forEach((row, index) => {
                    console.log(`Row ${index + 1}: ${row.join(' | ')}`);
                });
            } else {
                console.log('No table data found');
            }
        } else {
            console.log('No customer table found. Let me try to extract any visible customer data...');
            
            // Try to extract any customer-related data from the page
            const customerData = await page.evaluate(() => {
                // Look for common customer data patterns
                const customerElements = document.querySelectorAll(
                    '.customer, .client, [data-customer], [class*="customer"], [class*="client"]'
                );
                
                let data = [];
                customerElements.forEach(element => {
                    const text = element.textContent.trim();
                    if (text && text.length > 0) {
                        data.push(text.replace(/,/g, ';'));
                    }
                });
                
                return data;
            });
            
            if (customerData.length > 0) {
                const csvContent = customerData.join('\n');
                const filename = `customer-data-${new Date().toISOString().split('T')[0]}.csv`;
                fs.writeFileSync(filename, csvContent, 'utf8');
                console.log(`Customer data saved to ${filename}`);
                console.log(`Total items extracted: ${customerData.length}`);
            } else {
                console.log('No customer data found on the page');
            }
        }
        
        // Take a screenshot for debugging
        await page.screenshot({ path: 'page-screenshot.png', fullPage: true });
        console.log('Screenshot saved as page-screenshot.png');
        
    } catch (error) {
        console.error('Error extracting customer data:', error);
        
        // Take screenshot on error for debugging
        try {
            await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
            console.log('Error screenshot saved as error-screenshot.png');
        } catch (screenshotError) {
            console.error('Could not take error screenshot:', screenshotError);
        }
    } finally {
        await browser.close();
    }
}

// Run the extraction
extractCustomerData().catch(console.error);
